2025-09-12 19:38:55,679 - __main__ - INFO - ==========================================
2025-09-12 19:38:55,680 - __main__ - INFO - AlphaPortfolio训练开始 - 2025-09-12 19:38:55
2025-09-12 19:38:55,680 - __main__ - INFO - 日志保存路径: logs\training_20250912_193855.log
2025-09-12 19:38:55,681 - __main__ - INFO - ==========================================
2025-09-12 19:38:55,690 - __main__ - INFO - 训练参数:
2025-09-12 19:38:55,691 - __main__ - INFO -   index_type: hs300
2025-09-12 19:38:55,691 - __main__ - INFO -   start_year: 2023
2025-09-12 19:38:55,691 - __main__ - INFO -   end_year: 2023
2025-09-12 19:38:55,692 - __main__ - INFO -   test_size: 0.2
2025-09-12 19:38:55,692 - __main__ - INFO -   window_size: 5
2025-09-12 19:38:55,692 - __main__ - INFO -   batch_size: 8
2025-09-12 19:38:55,692 - __main__ - INFO -   num_episodes: 2
2025-09-12 19:38:55,692 - __main__ - INFO -   learning_rate: 0.0001
2025-09-12 19:38:55,692 - __main__ - INFO -   gamma: 0.99
2025-09-12 19:38:55,693 - __main__ - INFO -   initial_balance: 1000000.0
2025-09-12 19:38:55,695 - __main__ - INFO -   transaction_cost_pct: 0.001
2025-09-12 19:38:55,696 - __main__ - INFO -   model_path: models/alpha_portfolio.pth
2025-09-12 19:38:55,696 - __main__ - INFO -   top_stocks: 5
2025-09-12 19:38:55,697 - __main__ - INFO -   use_core_features: False
2025-09-12 19:38:55,698 - __main__ - INFO -   feature_list: None
2025-09-12 19:38:55,698 - __main__ - INFO -   debug: False
2025-09-12 19:38:55,703 - __main__ - INFO -   eval_frequency: 1
2025-09-12 19:38:55,706 - __main__ - INFO -   early_stopping_patience: 5
2025-09-12 19:38:55,708 - __main__ - INFO -   G: 2
2025-09-12 19:38:55,708 - __main__ - INFO - ==========================================
2025-09-12 19:38:55,709 - __main__ - INFO - Using device: cpu
2025-09-12 19:38:55,712 - __main__ - INFO - 初始化训练可视化工具
2025-09-12 19:38:55,719 - __main__ - INFO - 创建模型子目录: models\20250912_193855
2025-09-12 19:38:55,729 - __main__ - INFO - 加载和处理数据...
2025-09-12 19:38:55,764 - data_processor - INFO - 数据集基础目录: C:\Users\<USER>\Desktop\Work\Mingde\RL\RL优化需求_20250911\RL优化需求_20250911\AlphaPortfolio\alphaportfolio-ashare原始\alphaportfolio-ashare原始\alphaportfolio-ashare\dataset\hs300
2025-09-12 19:38:55,792 - data_processor - INFO - 读取数据文件: C:\Users\<USER>\Desktop\Work\Mingde\RL\RL优化需求_20250911\RL优化需求_20250911\AlphaPortfolio\alphaportfolio-ashare原始\alphaportfolio-ashare原始\alphaportfolio-ashare\dataset\hs300\2023-01-03_2023-12-29\factors_hs300_20230103_20231229.csv
2025-09-12 19:39:00,608 - data_processor - INFO - 合并后数据的字段: ['trade_date', 'ts_code', 'a2me', 'oa', 'aoa', 'beme', 'cash_asset', 'c2d', 'cto', 'dept2p', 'delta_ceq', 'delta_gm_sales', 'delta_so', 'delta_shrout', 'delta_pi2a', 'e2p', 'eps', 'free_cf', 'investment', 'ipm', 'ivc', 'lev', 'ldp', 'me', 'turnover', 'noa', 'ol', 'pcm', 'pm', 'prof', 'q_rate', 'ret', 'ret_max', 'rna', 'roa', 'roc', 'roe', 'roic', 's2c', 'sale_g', 'sat', 's2p', 'sga2s', 'std_turnover', 'std_vol', 'tan', 'total_vol', 'share_buyback_ratio', 'volume_ratio', 'ap_turnover', 'rnd_to_revenue', 'main_revenue_cash_ratio', 'surplus_cash_coverage', 'ar_turnover', 'op_cashflow_ratio', 'cash_dividend_coverage', 'inverse_ps_ratio', 'inverse_pb_ratio', 'inverse_pcf_ratio', 'inverse_pe_ratio', 'forecast_revenue_growth', 'forecast_profit_growth', 'forecast_eps_growth', 'forecast_pe', 'forecast_dividend_yield', 'forecast_roe', 'forecast_high_price_rate', 'forecast_low_price_rate', 'margin_balance_growth', 'short_selling_balance_growth', 'net_inflow_5d_avg_ratio', 'gdp_growth', 'cpi_yoy', 'ppi_yoy', 'pmi_yoy', 'mom_1m', 'clo_ratemean_1m', 'high_low_mean', 'skew_ret_1m', 'turn_cov_1m', 'cov_ret_amt', 'cov_volume_1m', 'price_range_1m', 'min_low_idx_20', 'close20_close', 'vol_price_bias_6', 'vol_up_momentum_pct', 'price_relative_pos_20d', 'vol_mom_9_26', 'price_change_rate_6', 'neg_corr_open_vol_10', 'cov_rank_high_vol_5', 'price_delta_indicator_9', 'open', 'high', 'low', 'close', 'vol', 'amount']
2025-09-12 19:39:04,074 - data_processor - INFO - 已选择交易量最大的前5支股票，实际股票数量: 5
2025-09-12 19:39:04,077 - data_processor - INFO - 成功处理数据，共 1210 行，股票数: 5，特征数: 103
2025-09-12 19:39:04,102 - __main__ - INFO - 数据集信息: 242个交易日, 5支股票, 103个特征
2025-09-12 19:39:04,102 - __main__ - INFO - 创建交易环境...
2025-09-12 19:39:04,155 - environment - INFO - 环境初始化: 股票数量=5, 特征数量=98, 窗口大小=5, 交易日数量=242
2025-09-12 19:39:04,157 - environment - INFO - 环境重置: 起始日期=2023-01-03 00:00:00, 结束日期=2023-12-29 00:00:00, 当前日期=2023-01-03 00:00:00
2025-09-12 19:39:04,201 - __main__ - INFO - 创建模型...
2025-09-12 19:39:04,201 - __main__ - INFO - 特征维度: 98
2025-09-12 19:39:04,202 - __main__ - INFO - 观察空间形状: (5, 5, 98)
2025-09-12 19:39:04,202 - __main__ - INFO - 实际特征列数: 98
2025-09-12 19:39:04,204 - __main__ - INFO - 使用的输入维度: 98
2025-09-12 19:39:10,612 - __main__ - INFO - 创建训练器...
2025-09-12 19:39:10,612 - Trainer - INFO - 初始化训练器
2025-09-12 19:39:10,614 - Trainer - INFO - 使用SafeReplayBuffer替代标准ReplayBuffer以提高数值稳定性
2025-09-12 19:39:10,614 - __main__ - INFO - 开始训练...
2025-09-12 19:39:10,624 - environment - INFO - 环境重置: 起始日期=2023-01-03 00:00:00, 结束日期=2023-12-29 00:00:00, 当前日期=2023-01-03 00:00:00
2025-09-12 19:39:40,749 - environment - INFO - 步骤 50, 日期 2023-03-21 00:00:00, 投资组合价值 667.47, 回报 0.4992, 平均回报 -0.0282, 奖励 -0.0377
2025-09-12 19:40:10,342 - environment - INFO - 步骤 100, 日期 2023-06-05 00:00:00, 投资组合价值 2.66, 回报 0.4996, 平均回报 0.0054, 奖励 0.0347
2025-09-12 19:40:39,437 - environment - INFO - 步骤 150, 日期 2023-08-16 00:00:00, 投资组合价值 1.50, 回报 0.4987, 平均回报 -0.0114, 奖励 -0.0583
2025-09-12 19:41:10,050 - environment - INFO - 步骤 200, 日期 2023-11-02 00:00:00, 投资组合价值 1.50, 回报 0.4987, 平均回报 -0.0370, 奖励 -0.1094
2025-09-12 19:41:36,675 - environment - INFO - 已到达数据末尾，训练结束
2025-09-12 19:41:37,274 - Trainer - INFO - Episode 0 | avg_reward=-0.0461 | loss=0.018111 | steps=242 | epsilon=0.9980 | nan=False
2025-09-12 19:41:37,275 - environment - INFO - 环境重置: 起始日期=2023-01-03 00:00:00, 结束日期=2023-12-29 00:00:00, 当前日期=2023-01-03 00:00:00
2025-09-12 19:41:37,306 - models - INFO - 添加批次维度后的形状: torch.Size([1, 5, 5, 98])
2025-09-12 19:41:45,496 - environment - INFO - 步骤 50, 日期 2023-03-21 00:00:00, 投资组合价值 680.12, 回报 0.4999, 平均回报 -0.0278, 奖励 -0.0368
2025-09-12 19:41:54,185 - environment - INFO - 步骤 100, 日期 2023-06-05 00:00:00, 投资组合价值 2.76, 回报 0.4999, 平均回报 0.0059, 奖励 0.0356
2025-09-12 19:42:03,488 - environment - INFO - 步骤 150, 日期 2023-08-16 00:00:00, 投资组合价值 1.50, 回报 0.4998, 平均回报 -0.0109, 奖励 -0.0570
2025-09-12 19:42:13,070 - environment - INFO - 步骤 200, 日期 2023-11-02 00:00:00, 投资组合价值 1.50, 回报 0.4999, 平均回报 -0.0364, 奖励 -0.1079
2025-09-12 19:42:21,200 - environment - INFO - 已到达数据末尾，训练结束
2025-09-12 19:42:21,406 - Trainer - WARNING - [IR计算失败] benchmark 加载失败: [Errno 2] No such file or directory: '/tmp/pycharm_project_525/alphaportfolio-ashare/dataset/benchmark_returns.csv'
2025-09-12 19:42:21,407 - Trainer - INFO - [回合 1/1] Reward=-10.894294, ARR=-1.000000, AVol=6.546797, MDD=-0.999999, ASR=1.209185, CR=-1.000001, IR=0.000000, WinRate=0.427386
2025-09-12 19:42:21,570 - Trainer - INFO - 模型已保存到: /root/autodl-tmp/models\20250912_193855\episode_1.pth
2025-09-12 19:42:21,572 - Trainer - INFO - 当前回合: 1
2025-09-12 19:42:21,573 - Trainer - INFO - 最佳奖励: -10.89429404816556
2025-09-12 19:42:21,573 - Trainer - INFO - 时间戳: 20250912_194221
2025-09-12 19:42:21,574 - Trainer - INFO - [模型已保存] Episode 1 -> /root/autodl-tmp/models\20250912_193855\episode_1.pth
2025-09-12 19:42:21,575 - environment - INFO - 环境重置: 起始日期=2023-01-03 00:00:00, 结束日期=2023-12-29 00:00:00, 当前日期=2023-01-03 00:00:00
2025-09-12 19:42:53,428 - environment - INFO - 步骤 50, 日期 2023-03-21 00:00:00, 投资组合价值 668.30, 回报 0.4994, 平均回报 -0.0282, 奖励 -0.0378
2025-09-12 19:43:23,163 - environment - INFO - 步骤 100, 日期 2023-06-05 00:00:00, 投资组合价值 2.66, 回报 0.4992, 平均回报 0.0054, 奖励 0.0345
2025-09-12 19:43:54,100 - environment - INFO - 步骤 150, 日期 2023-08-16 00:00:00, 投资组合价值 1.50, 回报 0.4995, 平均回报 -0.0114, 奖励 -0.0581
2025-09-12 19:44:25,138 - environment - INFO - 步骤 200, 日期 2023-11-02 00:00:00, 投资组合价值 1.50, 回报 0.4986, 平均回报 -0.0369, 奖励 -0.1094
2025-09-12 19:44:51,422 - environment - INFO - 已到达数据末尾，训练结束
2025-09-12 19:44:52,284 - Trainer - INFO - Episode 1 | avg_reward=-0.0461 | loss=0.006489 | steps=242 | epsilon=0.9960 | nan=False
2025-09-12 19:44:52,286 - environment - INFO - 环境重置: 起始日期=2023-01-03 00:00:00, 结束日期=2023-12-29 00:00:00, 当前日期=2023-01-03 00:00:00
2025-09-12 19:45:01,361 - environment - INFO - 步骤 50, 日期 2023-03-21 00:00:00, 投资组合价值 679.79, 回报 0.4999, 平均回报 -0.0278, 奖励 -0.0369
2025-09-12 19:45:10,661 - environment - INFO - 步骤 100, 日期 2023-06-05 00:00:00, 投资组合价值 2.76, 回报 0.4999, 平均回报 0.0059, 奖励 0.0356
2025-09-12 19:45:20,739 - environment - INFO - 步骤 150, 日期 2023-08-16 00:00:00, 投资组合价值 1.50, 回报 0.4999, 平均回报 -0.0109, 奖励 -0.0570
2025-09-12 19:45:29,247 - environment - INFO - 步骤 200, 日期 2023-11-02 00:00:00, 投资组合价值 1.50, 回报 0.4999, 平均回报 -0.0364, 奖励 -0.1078
2025-09-12 19:45:36,605 - environment - INFO - 已到达数据末尾，训练结束
2025-09-12 19:45:36,726 - Trainer - WARNING - [IR计算失败] benchmark 加载失败: [Errno 2] No such file or directory: '/tmp/pycharm_project_525/alphaportfolio-ashare/dataset/benchmark_returns.csv'
2025-09-12 19:45:36,726 - Trainer - INFO - [回合 1/1] Reward=-10.887718, ARR=-1.000000, AVol=6.547000, MDD=-0.999999, ASR=1.209335, CR=-1.000001, IR=0.000000, WinRate=0.427386
2025-09-12 19:45:36,828 - Trainer - INFO - 模型已保存到: /root/autodl-tmp/models\20250912_193855\episode_2.pth
2025-09-12 19:45:36,834 - Trainer - INFO - 当前回合: 2
2025-09-12 19:45:36,834 - Trainer - INFO - 最佳奖励: -10.887718473474646
2025-09-12 19:45:36,835 - Trainer - INFO - 时间戳: 20250912_194536
2025-09-12 19:45:36,835 - Trainer - INFO - [模型已保存] Episode 2 -> /root/autodl-tmp/models\20250912_193855\episode_2.pth
2025-09-12 19:45:36,916 - visualization - INFO - 训练指标已保存到: results\training_metrics_20250912_193855.csv
2025-09-12 19:45:36,918 - visualization - INFO - 评估指标已保存到: results\evaluation_metrics_20250912_193855.csv
2025-09-12 19:45:36,918 - __main__ - INFO - 训练成功完成!
