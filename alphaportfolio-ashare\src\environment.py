import gymnasium as gym
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple
from gymnasium import spaces

class PortfolioEnv(gym.Env):
    """投资组合交易环境"""
    
    def __init__(self, 
                 data: pd.DataFrame,
                 initial_balance: float = 1000000.0,
                 transaction_cost_pct: float = 0.001,
                 time_cost_pct: float = 0.0001,
                 window_size: int = 50,
                 print_verbosity: int = 50):
        """
        初始化交易环境
        
        Args:
            data: 股票数据，包含多只股票的价格和特征信息
            initial_balance: 初始资金
            transaction_cost_pct: 交易成本百分比
            time_cost_pct: 时间成本百分比
            window_size: 观察窗口大小
            print_verbosity: 日志打印频率，每隔多少步打印一次信息
        """
        super(PortfolioEnv, self).__init__()
        
        # 初始化日志
        self.logger = logging.getLogger(__name__)
        
        self.data = data
        self.window_size = window_size
        self.initial_balance = initial_balance
        self.transaction_cost_pct = transaction_cost_pct
        self.time_cost_pct = time_cost_pct
        self.print_verbosity = print_verbosity
        
        # 获取特征列（排除日期和股票代码）
        self.feature_columns = [col for col in data.columns if col not in ['trade_date', 'ts_code','vol','open','high','low','amount']]
        #self.feature_columns = [col for col in data.columns if col not in ['trade_date', 'ts_code']]
        # 获取股票数量和特征维度
        #print(len(data.columns))
        self.num_stocks = len(data['ts_code'].unique())
        self.num_features = len(self.feature_columns)
        #print(self.feature_columns)
        # 计算每个日期对应的所有股票数据
        # 使用字典加速查询
        self.date_map = {}
        for date, group in data.groupby('trade_date'):
            self.date_map[date] = group
            
        # 获取所有交易日期并排序
        self.dates = sorted(self.date_map.keys())
        
        # 获取所有唯一股票代码
        self.stock_codes = sorted(data['ts_code'].unique())
        
        self.logger.info(f"环境初始化: 股票数量={self.num_stocks}, 特征数量={self.num_features}, 窗口大小={window_size}, 交易日数量={len(self.dates)}")
        
        # 定义动作空间（每只股票的权重）
        self.action_space = spaces.Box(
            low=0, high=1,
            shape=(self.num_stocks,),
            dtype=np.float32
        )
        
        # 定义状态空间（历史窗口的特征）
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf,
            shape=(self.window_size, self.num_stocks, self.num_features),
            dtype=np.float32
        )
        
        self.reset()
        
    def reset(self) -> np.ndarray:
        """
        重置环境
        
        Returns:
            np.ndarray: 初始状态
        """
        self.current_step = 0
        self.balance = self.initial_balance
        self.current_weights = np.zeros(self.num_stocks)
        self.portfolio_value = self.initial_balance
        self.returns = []
        self.portfolio_values = [self.initial_balance]  # 记录组合价值历史
        self.current_date_idx = 0
        self.current_date = self.dates[self.current_date_idx] if self.current_date_idx < len(self.dates) else None
        
        # 初始化时打印环境信息
        self.logger.info(f"环境重置: 起始日期={self.dates[0]}, 结束日期={self.dates[-1]}, 当前日期={self.current_date}")
        
        return self._get_observation()
        
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """
        执行一步交易
        
        Args:
            action: 每只股票的目标权重
            
        Returns:
            tuple: (下一个状态, 奖励, 是否结束, 信息字典)
        """
        try:
            self.current_step += 1
            self.current_date_idx += 1
            
            # 检查是否已经到达数据末尾
            if self.current_date_idx >= len(self.dates):
                self.logger.info(f"已到达数据末尾，训练结束")
                return self._get_observation(), 0, True, self._get_info()
                
            self.current_date = self.dates[self.current_date_idx]
            
            # 确保权重和为1并且值域合理
            action = np.clip(action, 0.0, 1.0)
            action = self._normalize(action)
            
            # 计算每只股票的回报
            returns = self._calculate_returns()
            
            # 计算交易成本
            costs = self._calculate_costs(action)
            
            # 计算组合回报
            portfolio_return = np.sum(self.current_weights * returns) - costs
            
            # 裁剪回报，防止极端值
            portfolio_return = np.clip(portfolio_return, -0.5, 0.5)
            
            # 防止投资组合价值变为负数或过小
            self.portfolio_value *= (1 + portfolio_return)
            
            # 防止投资组合价值变为负数或过小
            self.portfolio_value = max(self.portfolio_value, 1.0)
            
            # 更新当前权重
            self.current_weights = action
            
            # 记录回报
            if np.isnan(portfolio_return) or np.isinf(portfolio_return):
                portfolio_return = 0.0
            self.returns.append(portfolio_return)
            self.portfolio_values.append(self.portfolio_value)
            
            # 计算奖励（滑动窗口夏普率，缩放并裁剪）
            reward = self._calculate_sharpe_ratio(window=10)
            reward = np.tanh(reward)
            reward = np.clip(reward, -1, 1)
            
            # 检查是否结束
            done = self.current_date_idx >= len(self.dates)

            # 定期打印当前状态
            if self.current_step % self.print_verbosity == 0 or done:
                avg_return = np.mean(self.returns[-self.print_verbosity:]) if len(self.returns) >= self.print_verbosity else np.mean(self.returns)
                self.logger.info(f"步骤 {self.current_step}, 日期 {self.current_date}, 投资组合价值 {self.portfolio_value:.2f}, "
                      f"回报 {portfolio_return:.4f}, 平均回报 {avg_return:.4f}, 奖励 {reward:.4f}")
            
            # 获取下一个状态
            next_state = self._get_observation()
            
            # 获取信息字典
            info = self._get_info()
            
            return next_state, reward, done, info
        
        except Exception as e:
            self.logger.error(f"执行交易步骤时出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            
            # 在错误情况下返回安全值
            next_state = self._get_observation()
            return next_state, 0.0, True, self._get_info()
        
    def _normalize(self, weights: np.ndarray) -> np.ndarray:
        """归一化权重"""
        return weights / (weights.sum() + 1e-8)
        
    def _calculate_returns(self) -> np.ndarray:
        """计算每只股票的回报"""
        try:
            current_prices = self.data.iloc[self.current_step][['close']].values
            prev_prices = self.data.iloc[self.current_step - 1][['close']].values
            
            # 防止除以零
            prev_prices = np.where(prev_prices < 1e-6, 1e-6, prev_prices)
            
            returns = (current_prices - prev_prices) / prev_prices
            
            # 裁剪极端回报值，防止数值不稳定
            returns = np.clip(returns, -0.5, 0.5)
            
            return returns.flatten()
        except Exception as e:
            self.logger.error(f"计算回报时出错: {str(e)}")
            # 在出错时返回零回报
            return np.zeros(self.num_stocks)
        
    def _calculate_costs(self, new_weights: np.ndarray) -> float:
        """
        计算交易成本
        
        Args:
            new_weights: 新的目标权重
            
        Returns:
            float: 总成本
        """
        try:
            # 交易成本
            weight_diff = np.abs(new_weights - self.current_weights)
            transaction_cost = np.sum(weight_diff) * self.transaction_cost_pct
            
            # 时间成本
            time_cost = self.time_cost_pct
            
            # 裁剪总成本，防止极端值
            total_cost = np.clip(transaction_cost + time_cost, 0.0, 0.1)
            
            return total_cost
        except Exception as e:
            self.logger.error(f"计算成本时出错: {str(e)}")
            return self.time_cost_pct  # 返回最小成本
        
    def _calculate_sharpe_ratio(self, window=10) -> float:
        """
        计算夏普比率
        
        Returns:
            float: 夏普比率
        """
        try:
            if len(self.returns) < 2:
                return 0
            # 只用最近window步
            returns = np.array(self.returns[-window:])
            returns = np.nan_to_num(returns, nan=0.0, posinf=0.0, neginf=0.0)
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            if std_return < 1e-8:
                std_return = 1e-8
            sharpe = mean_return / std_return
            sharpe = np.clip(sharpe, -10.0, 10.0)
            if np.isnan(sharpe) or np.isinf(sharpe):
                sharpe = 0.0
            return sharpe
        except Exception as e:
            self.logger.error(f"计算夏普比率时出错: {str(e)}")
            return 0.0
        
    def _get_info(self) -> Dict:
        """获取当前环境信息"""
        # 创建权重与股票代码的映射，确保权重是标量
        weight_map = {}
        for code, weight in zip(self.stock_codes, self.current_weights):
            # 确保权重是标量值
            if hasattr(weight, 'shape') and weight.shape:  # 如果是数组
                w_value = weight.item() if weight.size == 1 else float(weight.mean())
            elif isinstance(weight, (list, tuple)) and len(weight) > 0:  # 如果是列表
                w_value = float(np.mean(weight))
            else:  # 标量情况
                w_value = float(weight)
            weight_map[code] = w_value
        
        # 创建权重排序（降序）的列表，包含股票代码和权重
        sorted_weights = sorted(weight_map.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'date': self.current_date,
            'portfolio_value': self.portfolio_value,
            'returns': self.returns[-1] if self.returns else 0.0,
            'weights': self.current_weights,
            'stock_codes': self.stock_codes,  # 股票代码列表
            'weight_map': weight_map,  # 股票代码-权重映射
            'sorted_weights': sorted_weights,  # 按权重排序的列表
            'portfolio_values': self.portfolio_values,  # 投资组合价值历史
            'cumulative_return': self.portfolio_value / self.initial_balance - 1  # 累计回报率
        }

    def _get_observation(self):
        """获取当前观察，支持填充，使得每一日都能生成 observation"""
        try:
            end_idx = self.current_date_idx + 1  # 当前日期也应包含进 observation
            start_idx = max(0, end_idx - self.window_size)

            actual_window_size = end_idx - start_idx
            observation = np.zeros((self.window_size, self.num_stocks, self.num_features))
            window_dates = self.dates[start_idx:end_idx]

            for i, date in enumerate(window_dates):
                date_data = self.date_map.get(date)
                if date_data is None:
                    self.logger.warning(f"警告: 日期 {date} 没有数据")
                    continue

                for j, stock_code in enumerate(self.stock_codes):
                    stock_data = date_data[date_data['ts_code'] == stock_code]
                    if len(stock_data) == 0:
                        continue

                    stock_features = stock_data[self.feature_columns].values[0]
                    stock_features = np.nan_to_num(stock_features, nan=0.0)

                    # 可选的特征归一化
                    for k in range(stock_features.shape[0]):
                        feature_value = stock_features[k]
                        if np.abs(feature_value) > 1e-6:
                            feature_max = np.max(np.abs(feature_value))
                            stock_features[k] = feature_value / feature_max

                    stock_features = np.clip(stock_features, -10.0, 10.0)
                    observation[self.window_size - actual_window_size + i, j, :] = stock_features

            observation = np.nan_to_num(observation, nan=0.0, posinf=0.0, neginf=0.0)
            return observation

        except Exception as e:
            self.logger.error(f"获取观察时出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return np.zeros((self.window_size, self.num_stocks, self.num_features))

    # def _get_observation(self):
    #     """获取当前观察
    #
    #     Returns:
    #         np.ndarray: 当前观察，形状为 (window_size, num_stocks, num_features)
    #     """
    #     try:
    #         # 获取当前窗口的日期范围
    #         start_idx = self.current_date_idx - self.window_size
    #         end_idx = self.current_date_idx
    #
    #         if start_idx < 0:
    #             raise ValueError(f"窗口起始索引 {start_idx} 小于0，需要至少 {self.window_size} 天的历史数据")
    #
    #         window_dates = self.dates[start_idx:end_idx]
    #
    #         # 初始化观察数组 - 形状为 [window_size, num_stocks, num_features]
    #         observation = np.zeros((self.window_size, self.num_stocks, self.num_features))
    #
    #         # 为每个日期的每只股票填充特征
    #         for i, date in enumerate(window_dates):
    #             date_data = self.date_map.get(date)
    #
    #             if date_data is None:
    #                 self.logger.warning(f"警告: 日期 {date} 没有数据")
    #                 continue
    #
    #             for j, stock_code in enumerate(self.stock_codes):
    #                 # 获取当前股票在当前日期的数据
    #                 stock_data = date_data[date_data['ts_code'] == stock_code]
    #
    #                 if len(stock_data) == 0:
    #                     # 如果当天没有这只股票的数据，使用零填充
    #                     continue
    #
    #                 # 获取特征值 - 仅使用feature_columns中的列
    #                 stock_features = stock_data[self.feature_columns].values[0]
    #
    #                 # 将NaN值替换为0
    #                 stock_features = np.nan_to_num(stock_features, nan=0.0)
    #
    #                 # 特征归一化 - 对每个特征维度应用简单归一化
    #                 for k in range(stock_features.shape[0]):
    #                     feature_value = stock_features[k]
    #                     if np.abs(feature_value) > 1e-6:  # 防止除以零
    #                         feature_max = np.max(np.abs(feature_value))
    #                         stock_features[k] = feature_value / feature_max
    #
    #                 # 裁剪极端值
    #                 stock_features = np.clip(stock_features, -10.0, 10.0)
    #
    #                 # 填充观察数组
    #                 observation[i, j, :] = stock_features
    #
    #         observation = np.nan_to_num(observation, nan=0.0, posinf=0.0, neginf=0.0)
    #         return observation
    #     except Exception as e:
    #         self.logger.error(f"获取观察时出错: {str(e)}")
    #         import traceback
    #         self.logger.error(traceback.format_exc())
    #         # 在出错时返回零观察
    #         return np.zeros((self.window_size, self.num_stocks, self.num_features))
        
    def render(self, mode='human'):
        """渲染环境"""
        self.logger.info(f'Step: {self.current_step}')
        self.logger.info(f'Portfolio Value: {self.portfolio_value:.2f}')
        self.logger.info(f'Current Weights: {self.current_weights}')
        self.logger.info(f'Current Returns: {self.returns[-1]:.4f}')
        
    def _preprocess_data(self, data: np.ndarray) -> np.ndarray:
        """预处理数据，增加数值稳定性"""
        # 1. 处理无穷值
        data = np.nan_to_num(data, nan=0.0, posinf=1e6, neginf=-1e6)
        
        # 2. 标准化
        mean = np.mean(data, axis=0, keepdims=True)
        std = np.std(data, axis=0, keepdims=True)
        std = np.clip(std, 1e-6, None)  # 防止除零
        data = (data - mean) / std
        
        # 3. 裁剪异常值
        data = np.clip(data, -10, 10)
        
        return data 