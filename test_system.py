#!/usr/bin/env python3
"""
Simple test script to verify the AlphaPortfolio system is working
"""

import sys
import os
import traceback

# Add src directory to path
sys.path.append('src')

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"✗ PyTorch import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print(f"✓ Pandas {pd.__version__}")
    except ImportError as e:
        print(f"✗ Pandas import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    
    try:
        from data_processor import AShareDataProcessor
        print("✓ Data processor imported")
    except ImportError as e:
        print(f"✗ Data processor import failed: {e}")
        return False
    
    try:
        from models import AlphaPortfolio
        print("✓ AlphaPortfolio model imported")
    except ImportError as e:
        print(f"✗ AlphaPortfolio model import failed: {e}")
        return False
    
    try:
        from environment import PortfolioEnv
        print("✓ Portfolio environment imported")
    except ImportError as e:
        print(f"✗ Portfolio environment import failed: {e}")
        return False
    
    return True

def test_data_loading():
    """Test if data can be loaded"""
    print("\nTesting data loading...")
    
    try:
        from data_processor import AShareDataProcessor
        
        # Create data processor
        data_processor = AShareDataProcessor(
            index_type='hs300',
            top_stocks=5  # Use only 5 stocks for testing
        )
        print("✓ Data processor created")
        
        # Try to load some data
        data = data_processor.prepare_training_data(
            start_year=2023,
            end_year=2023
        )
        
        if data is not None:
            print(f"✓ Data loaded: {len(data)} rows, {len(data.columns)} columns")
            print(f"  Date range: {data['trade_date'].min()} to {data['trade_date'].max()}")
            print(f"  Stocks: {data['ts_code'].nunique()}")
            return True
        else:
            print("✗ Data loading returned None")
            return False
            
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        traceback.print_exc()
        return False

def test_model_creation():
    """Test if model can be created"""
    print("\nTesting model creation...")
    
    try:
        from models import AlphaPortfolio
        import torch
        
        # Create a simple model
        model = AlphaPortfolio(
            input_dim=10,  # Simple test dimension
            d_model=64,
            num_heads=2,
            d_ff=128,
            num_layers=1,
            dropout=0.1,
            G=2
        )
        print("✓ AlphaPortfolio model created")
        
        # Test forward pass
        test_input = torch.randn(1, 5, 10)  # batch_size=1, seq_len=5, features=10
        with torch.no_grad():
            weights, attention = model(test_input)
        
        print(f"✓ Forward pass successful: weights shape {weights.shape}")
        return True
        
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("AlphaPortfolio System Test")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed!")
        return False
    
    # Test data loading
    if not test_data_loading():
        print("\n❌ Data loading tests failed!")
        return False
    
    # Test model creation
    if not test_model_creation():
        print("\n❌ Model creation tests failed!")
        return False
    
    print("\n" + "=" * 50)
    print("✅ All tests passed! System is working correctly.")
    print("=" * 50)
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
