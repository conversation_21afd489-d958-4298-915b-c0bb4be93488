2025-09-12 19:34:27,435 - __main__ - INFO - ==========================================
2025-09-12 19:34:27,436 - __main__ - INFO - AlphaPortfolio训练开始 - 2025-09-12 19:34:27
2025-09-12 19:34:27,436 - __main__ - INFO - 日志保存路径: logs\training_20250912_193427.log
2025-09-12 19:34:27,436 - __main__ - INFO - ==========================================
2025-09-12 19:34:27,445 - __main__ - INFO - 训练参数:
2025-09-12 19:34:27,445 - __main__ - INFO -   index_type: hs300
2025-09-12 19:34:27,446 - __main__ - INFO -   start_year: 2023
2025-09-12 19:34:27,446 - __main__ - INFO -   end_year: 2023
2025-09-12 19:34:27,446 - __main__ - INFO -   test_size: 0.2
2025-09-12 19:34:27,448 - __main__ - INFO -   window_size: 5
2025-09-12 19:34:27,448 - __main__ - INFO -   batch_size: 8
2025-09-12 19:34:27,448 - __main__ - INFO -   num_episodes: 2
2025-09-12 19:34:27,448 - __main__ - INFO -   learning_rate: 0.0001
2025-09-12 19:34:27,449 - __main__ - INFO -   gamma: 0.99
2025-09-12 19:34:27,449 - __main__ - INFO -   initial_balance: 1000000.0
2025-09-12 19:34:27,449 - __main__ - INFO -   transaction_cost_pct: 0.001
2025-09-12 19:34:27,450 - __main__ - INFO -   model_path: models/alpha_portfolio.pth
2025-09-12 19:34:27,450 - __main__ - INFO -   top_stocks: 5
2025-09-12 19:34:27,450 - __main__ - INFO -   use_core_features: False
2025-09-12 19:34:27,451 - __main__ - INFO -   feature_list: None
2025-09-12 19:34:27,451 - __main__ - INFO -   debug: False
2025-09-12 19:34:27,451 - __main__ - INFO -   eval_frequency: 1
2025-09-12 19:34:27,451 - __main__ - INFO -   early_stopping_patience: 5
2025-09-12 19:34:27,452 - __main__ - INFO -   G: 2
2025-09-12 19:34:27,452 - __main__ - INFO - ==========================================
2025-09-12 19:34:27,452 - __main__ - INFO - Using device: cpu
2025-09-12 19:34:27,454 - __main__ - INFO - 初始化训练可视化工具
2025-09-12 19:34:27,455 - __main__ - INFO - 创建模型子目录: models\20250912_193427
2025-09-12 19:34:27,456 - __main__ - INFO - 加载和处理数据...
2025-09-12 19:34:27,456 - data_processor - INFO - 数据集基础目录: C:\Users\<USER>\Desktop\Work\Mingde\RL\RL优化需求_20250911\RL优化需求_20250911\AlphaPortfolio\alphaportfolio-ashare原始\alphaportfolio-ashare原始\alphaportfolio-ashare\dataset\hs300
2025-09-12 19:34:27,461 - data_processor - INFO - 读取数据文件: C:\Users\<USER>\Desktop\Work\Mingde\RL\RL优化需求_20250911\RL优化需求_20250911\AlphaPortfolio\alphaportfolio-ashare原始\alphaportfolio-ashare原始\alphaportfolio-ashare\dataset\hs300\2023-01-03_2023-12-29\factors_hs300_20230103_20231229.csv
2025-09-12 19:34:32,402 - data_processor - INFO - 合并后数据的字段: ['trade_date', 'ts_code', 'a2me', 'oa', 'aoa', 'beme', 'cash_asset', 'c2d', 'cto', 'dept2p', 'delta_ceq', 'delta_gm_sales', 'delta_so', 'delta_shrout', 'delta_pi2a', 'e2p', 'eps', 'free_cf', 'investment', 'ipm', 'ivc', 'lev', 'ldp', 'me', 'turnover', 'noa', 'ol', 'pcm', 'pm', 'prof', 'q_rate', 'ret', 'ret_max', 'rna', 'roa', 'roc', 'roe', 'roic', 's2c', 'sale_g', 'sat', 's2p', 'sga2s', 'std_turnover', 'std_vol', 'tan', 'total_vol', 'share_buyback_ratio', 'volume_ratio', 'ap_turnover', 'rnd_to_revenue', 'main_revenue_cash_ratio', 'surplus_cash_coverage', 'ar_turnover', 'op_cashflow_ratio', 'cash_dividend_coverage', 'inverse_ps_ratio', 'inverse_pb_ratio', 'inverse_pcf_ratio', 'inverse_pe_ratio', 'forecast_revenue_growth', 'forecast_profit_growth', 'forecast_eps_growth', 'forecast_pe', 'forecast_dividend_yield', 'forecast_roe', 'forecast_high_price_rate', 'forecast_low_price_rate', 'margin_balance_growth', 'short_selling_balance_growth', 'net_inflow_5d_avg_ratio', 'gdp_growth', 'cpi_yoy', 'ppi_yoy', 'pmi_yoy', 'mom_1m', 'clo_ratemean_1m', 'high_low_mean', 'skew_ret_1m', 'turn_cov_1m', 'cov_ret_amt', 'cov_volume_1m', 'price_range_1m', 'min_low_idx_20', 'close20_close', 'vol_price_bias_6', 'vol_up_momentum_pct', 'price_relative_pos_20d', 'vol_mom_9_26', 'price_change_rate_6', 'neg_corr_open_vol_10', 'cov_rank_high_vol_5', 'price_delta_indicator_9', 'open', 'high', 'low', 'close', 'vol', 'amount']
2025-09-12 19:34:37,748 - data_processor - INFO - 已选择交易量最大的前5支股票，实际股票数量: 5
2025-09-12 19:34:37,754 - data_processor - INFO - 成功处理数据，共 1210 行，股票数: 5，特征数: 103
2025-09-12 19:34:37,776 - __main__ - INFO - 数据集信息: 242个交易日, 5支股票, 103个特征
2025-09-12 19:34:37,776 - __main__ - INFO - 创建交易环境...
2025-09-12 19:34:37,876 - environment - INFO - 环境初始化: 股票数量=5, 特征数量=98, 窗口大小=5, 交易日数量=242
2025-09-12 19:34:37,886 - environment - INFO - 环境重置: 起始日期=2023-01-03 00:00:00, 结束日期=2023-12-29 00:00:00, 当前日期=2023-01-03 00:00:00
2025-09-12 19:34:37,926 - __main__ - INFO - 创建模型...
2025-09-12 19:34:37,927 - __main__ - INFO - 特征维度: 98
2025-09-12 19:34:37,928 - __main__ - INFO - 观察空间形状: (5, 5, 98)
2025-09-12 19:34:37,928 - __main__ - INFO - 实际特征列数: 98
2025-09-12 19:34:37,929 - __main__ - INFO - 使用的输入维度: 98
2025-09-12 19:34:49,865 - __main__ - ERROR - An error occurred: ReduceLROnPlateau.__init__() got an unexpected keyword argument 'verbose'
2025-09-12 19:34:49,866 - __main__ - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Work\Mingde\RL\RL优化需求_20250911\RL优化需求_20250911\AlphaPortfolio\alphaportfolio-ashare原始\alphaportfolio-ashare原始\alphaportfolio-ashare\src\main.py", line 511, in main
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: ReduceLROnPlateau.__init__() got an unexpected keyword argument 'verbose'

