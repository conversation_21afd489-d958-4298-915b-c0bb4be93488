import warnings
warnings.filterwarnings("ignore")
import logging
import argparse
from datetime import datetime, timedelta
import torch
import os
from tqdm import tqdm
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import matplotlib
import torch.optim as optim

# 设置matplotlib支持中文显示
try:
    # 尝试使用系统已安装的中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    matplotlib.rcParams['font.family'] = 'sans-serif'
except:
    logging.warning("无法设置matplotlib中文字体，图表中文可能显示为方块")

from data_processor import AShareDataProcessor
from models import AlphaPortfolio
from environment import PortfolioEnv
from trainer import PortfolioTrainer
from visualization import TrainingVisualizer

def setup_logging(timestamp):
    """设置日志"""
    # 创建logs目录（如果不存在）- 使用相对路径
    logs_dir = os.path.join('logs')
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    if root_logger.handlers:
        root_logger.handlers.clear()
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # 创建文件处理器
    log_filename = os.path.join('logs', f'training_{timestamp}.log')
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    
    # 将处理器添加到根日志记录器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    print(f"日志将保存到: {os.path.abspath(log_filename)}")
    
    return log_filename

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='AlphaPortfolio Training')
    
    parser.add_argument('--index_type', type=str, default='hs300',
                      choices=['hs300', 'zz500', 'zz1000'],
                      help='指数类型')
    parser.add_argument('--start_year', type=int, required=True,
                      help='训练数据起始年份')
    parser.add_argument('--end_year', type=int, required=True,
                      help='训练数据结束年份')
    parser.add_argument('--test_size', type=float, default=0.2,
                      help='测试集比例')
    parser.add_argument('--window_size', type=int, default=50,
                      help='观察窗口大小')
    parser.add_argument('--batch_size', type=int, default=64,
                      help='批次大小')
    parser.add_argument('--num_episodes', type=int, default=1000,
                      help='训练回合数')
    parser.add_argument('--learning_rate', type=float, default=0.0001,
                      help='学习率')
    parser.add_argument('--gamma', type=float, default=0.99,
                      help='折扣因子')
    parser.add_argument('--initial_balance', type=float, default=1000000.0,
                      help='初始资金')
    parser.add_argument('--transaction_cost_pct', type=float, default=0.001,
                      help='交易成本百分比')
    parser.add_argument('--model_path', type=str, default='models/alpha_portfolio.pth',
                      help='模型保存路径')
    parser.add_argument('--top_stocks', type=int, default=None,
                      help='使用交易量最大的前N支股票，默认使用全部')
    parser.add_argument('--use_core_features', action='store_true',
                      help='仅使用核心特征子集，可显著加快训练')
    parser.add_argument('--feature_list', type=str, default=None,
                      help='要使用的特征列表，以逗号分隔，例如: "close,open,volume"')
    parser.add_argument('--debug', action='store_true',
                      help='启用调试模式，打印更多日志信息')
    parser.add_argument('--eval_frequency', type=int, default=20,
                      help='评估频率，每隔多少回合进行一次评估，数值越小评估越频繁')
    parser.add_argument('--early_stopping_patience', type=int, default=5,
                      help='提前停止的耐心度，连续多少次评估没有改善后停止训练')
    parser.add_argument('--G', type=int, default=10, 
                      help='多空组合的G参数，决定多头和空头各自的资产数量')
    
    return parser.parse_args()


# def evaluate_model(model, env, num_episodes, device):
#     """
#     离线评估模型
#
#     Args:
#         model: 要评估的模型
#         env: 评估环境
#         num_episodes: 评估回合数
#         device: 计算设备
#     """
#     model.eval()
#     trainer = PortfolioTrainer(model=model, device=device)
#     metrics = trainer.evaluate(env, num_episodes=num_episodes)
#
#     logger = logging.getLogger(__name__)
#     logger.info(
#         f"\n离线评估结果:\n"
#         f"平均奖励: {metrics['avg_reward']:.2f} (±{metrics['std_reward']:.2f})\n"
#         f"夏普比率: {metrics['avg_sharpe']:.2f} (±{metrics['std_sharpe']:.2f})\n"
#         f"最大回撤: {metrics['avg_max_drawdown']:.2f} (±{metrics['std_max_drawdown']:.2f})\n"
#         f"胜率: {metrics['avg_win_rate']:.2f} (±{metrics['std_win_rate']:.2f})"
#     )
#
#     return metrics


# def analyze_portfolio(model, env, device, top_n=10, timestamp=None):
#     """
#     详细分析投资组合
#
#     Args:
#         model: 训练好的模型
#         env: 评估环境
#         device: 计算设备
#         top_n: 显示前N个最大权重的股票
#         timestamp: 统一的时间戳
#     """
#     logger = logging.getLogger(__name__)
#     logger.info("\n==== 投资组合详细分析 ====")
#
#     model.eval()
#     state = env.reset()
#     done = False
#
#     # 模拟一个完整的测试周期
#     steps = 0
#     # 修正：使用交易日数量而不是数据集总行数
#     max_steps = len(env.dates) - env.window_size
#     logger.info(f"分析交易日数量: {max_steps}日 (总交易日: {len(env.dates)}, 窗口大小: {env.window_size})")
#     pbar = tqdm(total=max_steps, desc="投资组合分析", leave=True)
#
#     while not done:
#         with torch.no_grad():
#             state_tensor = torch.FloatTensor(state).to(device)
#             weights, attention = model(state_tensor)
#             action = weights.cpu().numpy()[0]
#
#         next_state, reward, done, info = env.step(action)
#         state = next_state
#         steps += 1
#
#         # 更新进度条
#         pbar.update(1)
#         pbar.set_postfix({
#             'step': steps,
#             'portfolio_value': f"{info['portfolio_value']:.2f}"
#         })
#
#     # 关闭进度条
#     pbar.close()
#
#     # 获取最终的投资组合
#     weight_map = info['weight_map']
#     sorted_weights = info['sorted_weights']
#
#     # 显示投资组合总览
#     total_stocks = len(weight_map)
#     active_positions = sum(1 for w in action if w > 0.01)  # 权重>1%的持仓
#
#     logger.info(f"总股票数量: {total_stocks}")
#     logger.info(f"实际持仓数量 (权重>1%): {active_positions}")
#     logger.info(f"投资组合集中度: 前{top_n}只股票占比 {sum(w for _, w in sorted_weights[:top_n]):.2f}")
#
#     # 显示前N个权重最大的股票
#     logger.info(f"\n前{top_n}大持仓:")
#     for i, (stock_code, weight) in enumerate(sorted_weights[:top_n], 1):
#         logger.info(f"{i}. {stock_code}: {weight:.4f} ({weight*100:.2f}%)")
#
#     # 显示行业分布 (如果有行业数据)
#     # 这部分需要额外的行业数据，可以根据需要实现
#
#     # 保存完整的投资组合数据到CSV
#     try:
#         # 创建results目录（如果不存在）
#         if not os.path.exists('results'):
#             os.makedirs('results')
#             logger.info("创建结果保存目录: results/")
#
#         portfolio_df = pd.DataFrame(sorted_weights, columns=['股票代码', '权重'])
#         portfolio_df['权重百分比'] = portfolio_df['权重'] * 100
#
#         # 使用results路径下的文件，包含时间戳
#         if timestamp:
#             output_file = os.path.join('results', f'portfolio_weights_{timestamp}.csv')
#         else:
#             output_file = os.path.join('results', 'portfolio_weights.csv')
#
#         portfolio_df.to_csv(output_file, index=False)
#         logger.info(f"\n完整投资组合权重已保存到: {output_file}")
#     except Exception as e:
#         logger.error(f"保存投资组合数据失败: {str(e)}")
#
#     model.train()
#     return sorted_weights


# def plot_training_curves(rewards_df, save_dir='results', timestamp=None):
#     """
#     直接根据训练记录绘制训练曲线
#
#     Args:
#         rewards_df: 训练记录DataFrame
#         save_dir: 保存目录
#         timestamp: 时间戳
#     """
#     logger = logging.getLogger(__name__)
#
#     # 确保保存目录存在
#     if not os.path.exists(save_dir):
#         os.makedirs(save_dir)
#
#     try:
#         plt.figure(figsize=(15, 10))
#
#         # 1. 奖励曲线
#         plt.subplot(2, 2, 1)
#         plt.plot(rewards_df['episode'], rewards_df['reward'], 'b-', label='奖励')
#
#         # 如果数据点足够多，添加平滑曲线
#         if len(rewards_df) > 10:
#             window_size = min(10, len(rewards_df) // 5)
#             rewards_df['smooth_reward'] = rewards_df['reward'].rolling(window=window_size).mean()
#             plt.plot(rewards_df['episode'], rewards_df['smooth_reward'], 'r-', linewidth=2,
#                     label=f'平滑奖励 (窗口={window_size})')
#
#         plt.title('训练奖励变化')
#         plt.xlabel('回合')
#         plt.ylabel('奖励')
#         plt.legend()
#
#         # 2. 损失曲线
#         plt.subplot(2, 2, 2)
#         if 'loss' in rewards_df.columns:
#             plt.plot(rewards_df['episode'], rewards_df['loss'], 'r-', label='损失')
#
#             # 如果数据点足够多，添加平滑曲线
#             if len(rewards_df) > 10:
#                 rewards_df['smooth_loss'] = rewards_df['loss'].rolling(window=window_size).mean()
#                 plt.plot(rewards_df['episode'], rewards_df['smooth_loss'], 'g-', linewidth=2,
#                         label=f'平滑损失 (窗口={window_size})')
#
#             plt.title('训练损失变化')
#             plt.xlabel('回合')
#             plt.ylabel('损失')
#             plt.legend()
#
#         # 3. 评估奖励曲线
#         plt.subplot(2, 2, 3)
#         if 'eval_reward' in rewards_df.columns:
#             # 过滤掉NaN值
#             eval_df = rewards_df[rewards_df['eval_reward'].notna()]
#             if not eval_df.empty:
#                 plt.plot(eval_df['episode'], eval_df['eval_reward'], 'g-', marker='o', label='评估奖励')
#                 plt.title('评估奖励变化')
#                 plt.xlabel('回合')
#                 plt.ylabel('评估奖励')
#                 plt.legend()
#
#         # 4. 探索率变化
#         plt.subplot(2, 2, 4)
#         if 'epsilon' in rewards_df.columns:
#             # 左Y轴显示探索率
#             plt.plot(rewards_df['episode'], rewards_df['epsilon'], 'b--', label='探索率ε')
#             plt.title('探索率变化')
#             plt.xlabel('回合')
#             plt.ylabel('ε值')
#             plt.tick_params(axis='y', labelcolor='b')
#
#             # 如果有夏普比率数据，在右Y轴显示
#             if 'eval_sharpe' in rewards_df.columns:
#                 eval_df = rewards_df[rewards_df['eval_sharpe'].notna()]
#                 if not eval_df.empty:
#                     ax2 = plt.twinx()
#                     ax2.plot(eval_df['episode'], eval_df['eval_sharpe'], 'g-', marker='o', label='夏普比率')
#                     ax2.set_ylabel('夏普比率', color='g')
#                     ax2.tick_params(axis='y', labelcolor='g')
#
#                     # 合并图例
#                     lines, labels = plt.gca().get_legend_handles_labels()
#                     lines2, labels2 = ax2.get_legend_handles_labels()
#                     ax2.legend(lines + lines2, labels + labels2, loc='best')
#                 else:
#                     plt.legend()
#             else:
#                 plt.legend()
#
#         # 显示图表
#         plt.tight_layout()
#
#         # 保存图表
#         if timestamp:
#             save_path = os.path.join(save_dir, f'training_summary_{timestamp}.png')
#         else:
#             save_path = os.path.join(save_dir, f'training_summary_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
#         plt.savefig(save_path)
#         plt.close()
#
#         logger.info(f"训练概览图已保存到: {save_path}")
#
#     except Exception as e:
#         logger.error(f"绘制训练曲线时出错: {str(e)}")
#         import traceback
#         logger.error(traceback.format_exc())


def get_available_device():
    """
    智能选择可用的计算设备
    
    Returns:
        torch.device: 可用的计算设备
    """
    if not torch.cuda.is_available():
        return torch.device('cpu')
        
    # 获取所有可用的 GPU 数量
    num_gpus = torch.cuda.device_count()
    
    # 如果没有 GPU 可用，返回 CPU
    if num_gpus == 0:
        return torch.device('cpu')
        
    # 尝试找到第一个可用的 GPU
    for i in range(num_gpus):
        try:
            # 尝试分配一些内存来测试 GPU 是否可用
            torch.cuda.set_device(i)
            torch.cuda.empty_cache()
            # 分配一个小张量来测试
            x = torch.zeros(1).cuda()
            del x
            torch.cuda.empty_cache()
            return torch.device(f'cuda:{i}')
        except Exception as e:
            logging.warning(f"GPU {i} 不可用: {str(e)}")
            continue
            
    # 如果所有 GPU 都不可用，返回 CPU
    return torch.device('cpu')

def main():
    """主函数"""
    # 生成统一的时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 设置日志
    log_file = setup_logging(timestamp)
    logger = logging.getLogger(__name__)
    
    # 记录训练开始信息
    logger.info(f"==========================================")
    logger.info(f"AlphaPortfolio训练开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"日志保存路径: {log_file}")
    logger.info(f"==========================================")
    
    # 解析参数
    args = parse_args()
    
    # 记录训练参数
    logger.info(f"训练参数:")
    for arg in vars(args):
        logger.info(f"  {arg}: {getattr(args, arg)}")
    logger.info(f"==========================================")
    
    # 设置设备
    device = get_available_device()
    logger.info(f'Using device: {device}')
    
    # 如果使用 GPU，打印 GPU 信息
    if device.type == 'cuda':
        logger.info(f'GPU 信息:')
        logger.info(f'  GPU 名称: {torch.cuda.get_device_name(device.index)}')
        logger.info(f'  GPU 内存: {torch.cuda.get_device_properties(device.index).total_memory / 1024**3:.2f} GB')
        logger.info(f'  CUDA 版本: {torch.version.cuda}')
    
    # 创建可视化工具
    results_dir = os.path.join('results')
    os.makedirs(results_dir, exist_ok=True)
    visualizer = TrainingVisualizer(save_dir=results_dir, timestamp=timestamp)
    logger.info("初始化训练可视化工具")
    
    try:
        # 确保模型保存目录存在
        model_dir = os.path.dirname(args.model_path)
        if model_dir and not os.path.exists(model_dir):
            os.makedirs(model_dir)
            logger.info(f"创建模型保存目录: {model_dir}")
            
        # 确保 models 目录存在（用于保存检查点）
        if not os.path.exists('models'):
            os.makedirs('models')
            logger.info("创建模型检查点目录: models/")

        # 创建 models/{timestamp} 子目录
        timestamp_model_dir = os.path.join('models', timestamp)
        os.makedirs(timestamp_model_dir, exist_ok=True)
        logger.info(f"创建模型子目录: {timestamp_model_dir}")

        # 设置特征子集
        feature_subset = None
        if args.use_core_features:
            # 获取核心特征列表
            data_processor = AShareDataProcessor(index_type=args.index_type)
            feature_subset = data_processor.get_core_features()
            logger.info(f"使用核心特征子集: {feature_subset}")
        elif args.feature_list:
            # 使用用户指定的特征列表
            feature_subset = [f.strip() for f in args.feature_list.split(',')]
            logger.info(f"使用用户指定特征: {feature_subset}")
            
        # 数据处理
        logger.info('加载和处理数据...')
        data_processor = AShareDataProcessor(
            index_type=args.index_type,
            top_stocks=args.top_stocks,
            feature_subset=feature_subset
        )
        data = data_processor.prepare_training_data(
            start_year=args.start_year,
            end_year=args.end_year
        )
        
        if data is None:
            logger.error('Failed to prepare training data')
            return
            
        # 打印数据集信息
        num_stocks = data['ts_code'].nunique()
        num_dates = data['trade_date'].nunique()
        num_features = len(data.columns) - 2  # 减去trade_date和ts_code
        logger.info(f"数据集信息: {num_dates}个交易日, {num_stocks}支股票, {num_features}个特征")
        
        # 创建环境
        logger.info('创建交易环境...')
        env = PortfolioEnv(
            data=data,
            initial_balance=args.initial_balance,
            transaction_cost_pct=args.transaction_cost_pct,
            window_size=args.window_size
        )
        
        # 创建模型
        logger.info('创建模型...')
        # 获取正确的特征维度
        input_dim = env.num_features  # 特征维度
        logger.info(f'特征维度: {input_dim}')
        logger.info(f'观察空间形状: {env.observation_space.shape}')  # 记录观察空间形状
        
        # 创建模型 - 确保使用正确的输入维度
        if hasattr(env, 'data') and 'trade_date' in env.data.columns and 'ts_code' in env.data.columns:
            # 特征列是除了日期和股票代码之外的所有列
            feature_columns = [col for col in env.data.columns if col not in ['trade_date', 'ts_code','vol','open','high','low','amount']]
            actual_feature_dim = len(feature_columns)
            logger.info(f'实际特征列数: {actual_feature_dim}')
            input_dim = actual_feature_dim
        
        logger.info(f'使用的输入维度: {input_dim}')
        model = AlphaPortfolio(
            input_dim=input_dim,
            d_model=256,
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0,
            G=args.G
        ).to(device)
        
        # 优化器
        optimizer = optim.AdamW(
            model.parameters(),
            lr=1e-4,  # 降低学习率
            weight_decay=0.01,  # 增加权重衰减
            eps=1e-8  # 增加数值稳定性
        )
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='max',
            factor=0.5,
            patience=5,
            min_lr=1e-6
        )
        
        # 创建训练器
        logger.info('创建训练器...')
        trainer = PortfolioTrainer(
            env=env,
            model=model,
            device=device,
            learning_rate=args.learning_rate,
            gamma=args.gamma,
            batch_size=args.batch_size,
            timestamp=timestamp  # 传递统一的时间戳
        )
        
        # 训练模型
        logger.info('开始训练...')
        rewards_df = trainer.train(
            env=env,
            num_episodes=args.num_episodes,
            eval_frequency=args.eval_frequency,
            early_stopping_patience=args.early_stopping_patience
        )
        
        # # 收集训练数据并生成可视化图表
        # logger.info('生成训练可视化图表...')
        # try:
        #     # 确保从trainer获取完整数据，特别是夏普比率和epsilon值
        #     if hasattr(trainer, 'episode_sharpe_ratios') and trainer.episode_sharpe_ratios:
        #         # 创建一个包含夏普比率的指标字典
        #         sharpe_metrics = {'sharpe_ratio': trainer.episode_sharpe_ratios}
        #         # 添加每个回合的夏普比率数据
        #         for i, sharpe in enumerate(trainer.episode_sharpe_ratios, 1):
        #             visualizer.add_eval_data(i, {'avg_sharpe': sharpe})
        #
        #     # 确保epsilon值也添加到可视化器
        #     if hasattr(trainer, 'epsilon_values') and trainer.epsilon_values:
        #         visualizer.epsilon_values = trainer.epsilon_values
        #
        #     # 将rewards_df转换为DataFrame（如果它是列表）
        #     if isinstance(rewards_df, list):
        #         rewards_df = pd.DataFrame(rewards_df)
        #
        #     # 使用TrainingVisualizer类生成图表
        #     for i, row in rewards_df.iterrows():
        #         visualizer.add_episode_data(
        #             episode=row['episode'],
        #             reward=row['reward'],
        #             loss=row['loss'] if 'loss' in row else 0
        #         )
        #
        #         # 如果有评估数据，也添加到可视化器
        #         if 'eval_reward' in row and not pd.isna(row['eval_reward']):
        #             metrics = {
        #                 'avg_reward': row['eval_reward'],
        #                 'avg_sharpe': row['eval_sharpe'] if 'eval_sharpe' in row else 0,
        #                 'avg_max_drawdown': row.get('eval_max_drawdown', 0),
        #                 'avg_win_rate': row.get('eval_win_rate', 0)
        #             }
        #             visualizer.add_eval_data(row['episode'], metrics)
        #
        #     # 绘制训练曲线
        #     #visualizer.plot_training_curves()
        #
        #     # 保存指标到CSV
        #     visualizer.save_metrics_to_csv()
        #
        #     # 直接使用DataFrame绘制训练概览图
        #     plot_training_curves(rewards_df, timestamp=timestamp)
        #
        #     # 显示日志
        #     logger.info("训练曲线已生成，保存在results目录下")
        # except Exception as e:
        #     logger.error(f"生成可视化图表失败: {str(e)}")
        #     import traceback
        #     logger.error(traceback.format_exc())

        # # 保存模型（添加时间戳到文件名）
        # logger.info('保存模型...')
        # model_path = f"models/alpha_portfolio_{timestamp}.pth"
        # trainer.save_model(model_path, timestamp=timestamp)
        for i, row in rewards_df.iterrows():
            visualizer.add_episode_data(
                episode=row['episode'],
                reward=row['reward'],
                loss=row['loss'] if 'loss' in row else 0
            )
        # 保存指标到CSV
        visualizer.save_metrics_to_csv_new(rewards_df)
        # # 分析最终投资组合
        # logger.info('分析最终投资组合...')
        # final_portfolio = analyze_portfolio(model, env, device, args.top_stocks, timestamp=timestamp)
        logger.info('训练成功完成!')
        
    except Exception as e:
        logger.error(f'An error occurred: {str(e)}')
        import traceback
        logger.error(traceback.format_exc())
        return

if __name__ == '__main__':
    main() 